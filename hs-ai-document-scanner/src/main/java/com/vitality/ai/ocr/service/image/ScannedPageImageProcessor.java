package com.vitality.ai.ocr.service.image;

import org.apache.commons.io.FileUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.random.RandomGenerator;

import static com.vitality.ai.ocr.service.image.ImageTransformations.addNoise;
import static com.vitality.ai.ocr.service.image.ImageTransformations.rotateImage;
import static com.vitality.ai.ocr.service.image.ImageTransformations.toGrayscale;

public class ScannedPageImageProcessor implements ImageProcessor {
    private final double noiseLevel;

    public ScannedPageImageProcessor() {
        this(0.5); // Default noise level
    }

    public ScannedPageImageProcessor(double noiseLevel) {
        this.noiseLevel = noiseLevel;
    }

    @Override
    public byte[] process(byte[] inputBytes) {
        try {
            BufferedImage original = ImageIO.read(new ByteArrayInputStream(inputBytes));
            if (original == null) {
                throw new ImageProcessorException("Invalid image");
            }

            BufferedImage image = toGrayscale(original);
            image = addNoise(image, noiseLevel);

            RandomGenerator random = RandomGenerator.getDefault();
            double angle = random.nextDouble(0.1, 0.9) * (random.nextBoolean() ? 1 : -1);

            image = rotateImage(image, angle); // Deskew

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(image, "png", out);
            return out.toByteArray();
        } catch (IOException e) {
            throw new ImageProcessorException(
                "Failed to process image of size " + FileUtils.byteCountToDisplaySize(inputBytes.length), e
            );
        }
    }

}
