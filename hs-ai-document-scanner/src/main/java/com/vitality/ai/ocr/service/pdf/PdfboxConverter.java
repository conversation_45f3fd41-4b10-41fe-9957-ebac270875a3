package com.vitality.ai.ocr.service.pdf;

import com.vitality.ai.ocr.service.image.ScannedPageImageProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Service for converting regular PDF documents into "scanned" PDFs where each page
 * is rendered as a raster image. This simulates the effect of physically scanning
 * a document, useful for testing OCR services or document processing workflows.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfboxConverter implements PdfConverter {

    private final PdfRenderer pdfRenderer;

    /**
     * Convert a PDF from byte array to a "scanned" PDF.
     *
     * @param inputBytes The input PDF as byte array
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    @Override
    public byte[] convertToScannedPdf(byte[] inputBytes) throws IOException {
        return convertToScannedPdf(inputBytes, PdfConstants.DEFAULT_DPI);
    }

    /**
     * Convert a PDF from byte array to a "scanned" PDF with specified DPI.
     *
     * @param inputBytes The input PDF as byte array
     * @param dpi        The resolution for rendering pages
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    @Override
    public byte[] convertToScannedPdf(byte[] inputBytes, int dpi) throws IOException {
        return convertToScannedPdf(inputBytes, dpi, 0.5);
    }

    @Override
    public byte[] convertToScannedPdf(byte[] inputBytes, int dpi, double noiseLevel) throws IOException {
        validateInputs(inputBytes, dpi);

        log.info("Starting conversion of PDF (size: {}) to scanned format at {} DPI with noise level {}",
            FileUtils.byteCountToDisplaySize(inputBytes.length), dpi, noiseLevel);

        try {
            long start = System.currentTimeMillis();

            byte[] scannedPdfBytes = convertDocumentToScannedPdf(inputBytes, dpi, noiseLevel);

            log.info("Successfully converted PDF (size: {}) to scanned PDF {} with {} DPI and noise {} in {} ms",
                FileUtils.byteCountToDisplaySize(inputBytes.length),
                FileUtils.byteCountToDisplaySize(scannedPdfBytes.length),
                dpi, noiseLevel, System.currentTimeMillis() - start);

            return scannedPdfBytes;
        } catch (Exception e) {
            log.error("Error converting PDF bytes to scanned format: {}", e.getMessage(), e);
            throw new IOException("Failed to convert PDF bytes to scanned format", e);
        }
    }

    /**
     * Core conversion logic that processes the PDDocument.
     */
    private byte[] convertDocumentToScannedPdf(byte[] inputBytes, int dpi, double noiseLevel) throws IOException {
        List<byte[]> renderedPages = pdfRenderer.renderPagesToImages(inputBytes, dpi, new ScannedPageImageProcessor(noiseLevel));

        try (PDDocument inputDocument = Loader.loadPDF(inputBytes)) {
            try (PDDocument outputDocument = new PDDocument()) {
                for (int i = 0; i < renderedPages.size(); i++) {
                    byte[] imageBytes = renderedPages.get(i);
                    PDPage originalPage = inputDocument.getPage(i);

                    addImagePageToDocument(outputDocument, imageBytes, originalPage.getMediaBox());
                }

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                outputDocument.save(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Add a BufferedImage as a page to the output PDF document.
     */
    private void addImagePageToDocument(PDDocument document, byte[] imageBytes, PDRectangle originalPageSize) throws IOException {
        // Create PDImageXObject from the image bytes
        PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, imageBytes, "page_image");

        // Create new page with original dimensions
        PDPage page = new PDPage(originalPageSize);
        document.addPage(page);

        // Calculate scaling to fit image to page while maintaining aspect ratio
        float pageWidth = originalPageSize.getWidth();
        float pageHeight = originalPageSize.getHeight();
        float imageWidth = pdImage.getWidth();
        float imageHeight = pdImage.getHeight();

        float scaleX = pageWidth / imageWidth;
        float scaleY = pageHeight / imageHeight;
        float scale = Math.min(scaleX, scaleY);

        float scaledWidth = imageWidth * scale;
        float scaledHeight = imageHeight * scale;

        // Center the image on the page
        float x = (pageWidth - scaledWidth) / 2;
        float y = (pageHeight - scaledHeight) / 2;

        // Draw the image on the page
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
        }
    }

    private void validateInputs(byte[] inputBytes, int dpi) {
        if (inputBytes == null || inputBytes.length == 0) {
            throw new IllegalArgumentException("Input bytes cannot be null or empty");
        }
        validateDpi(dpi);
    }

    private void validateDpi(int dpi) {
        if (dpi < PdfConstants.MIN_DPI) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output. Recommended: 300+");
        }
        if (dpi > PdfConstants.MAX_DPI) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }
}
