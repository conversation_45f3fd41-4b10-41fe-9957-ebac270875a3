package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.service.FileValidationService;
import com.vitality.ai.ocr.service.ZipArchiveService;
import com.vitality.ai.ocr.service.image.OcrOptimizedImageProcessor;
import com.vitality.ai.ocr.service.image.ScannedPageImageProcessor;
import com.vitality.ai.ocr.service.pdf.PdfConstants;
import com.vitality.ai.ocr.service.pdf.PdfRenderer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for rendering PDF pages to images.
 * This controller accepts PDF files and returns ZIP archives containing PNG images of each page.
 */
@Slf4j
@RestController
@RequestMapping("/api/pdf/render")
@Tag(name = "PDF Renderer", description = "Render PDF pages to images")
public class PdfRenderController extends BaseRestController {

    private final PdfRenderer pdfRenderer;
    private final ZipArchiveService zipArchiveService;
    private final FileValidationService fileValidationService;

    public PdfRenderController(PdfRenderer pdfRenderer, ZipArchiveService zipArchiveService,
                               FileValidationService fileValidationService, ObjectMapper objectMapper) {
        super(objectMapper);
        this.pdfRenderer = pdfRenderer;
        this.zipArchiveService = zipArchiveService;
        this.fileValidationService = fileValidationService;
    }

    @Operation(
        summary = "Convert PDF to Standard Images",
        description = "Renders each page of a PDF document into standard PNG images and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToImagesZip(
        @Parameter(
            description = "PDF file to render to images",
            required = true,
            content = @Content(mediaType = "application/pdf")
        )
        @RequestParam("file") MultipartFile file) throws IOException {

        log.info("Render PDF: {} (size: {} bytes) to standard images",
            file.getOriginalFilename(), file.getSize());

        List<byte[]> imagePages = pdfRenderer.renderPagesToImages(file.getBytes(), PdfConstants.DEFAULT_DPI);
        return createZipResponse(file, imagePages);
    }

    @Operation(
        summary = "Convert PDF to Scanned Images",
        description = "Renders PDF pages with scanned document effects (noise, rotation, artifacts) and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to scanned images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing scanned-style PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/scanned", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToScannedImagesZip(
        @Parameter(
            description = "PDF file to render with scanned effects",
            required = true,
            content = @Content(mediaType = "application/pdf")
        )
        @RequestParam("file") MultipartFile file,
        @Parameter(
            description = "Resolution for rendering pages in DPI (dots per inch). " +
                "Higher values produce better quality but larger files. " +
                "Minimum: 150, Recommended: 300, Maximum: 600",
            example = "300"
        )
        @RequestParam(value = "dpi", defaultValue = "300") int dpi,

        @Parameter(
            description = "Noise level for scanning effects (0.0-1.0). " +
                "0.0 = no noise, 0.5 = moderate noise, 1.0 = heavy noise",
            example = "0.5"
        )
        @RequestParam(value = "noiseLevel", defaultValue = "0.5") double noiseLevel) throws IOException {

        log.info("Render PDF: {} (size: {} bytes) to scanned images",
            file.getOriginalFilename(), file.getSize());

        List<byte[]> imagePages = pdfRenderer.renderPagesToImages(file.getBytes(), dpi, new ScannedPageImageProcessor(noiseLevel));
        return createZipResponse(file, imagePages);
    }

    @Operation(
        summary = "Convert PDF to OCR-Optimized Images",
        description = "Renders PDF pages optimized for OCR processing (enhanced contrast, noise reduction) and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to OCR-optimized images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing OCR-optimized PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/ocr-optimized", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToOcrOptimizedImagesZip(
        @Parameter(
            description = "PDF file to render with OCR optimizations",
            required = true,
            content = @Content(mediaType = "application/pdf")
        )
        @RequestParam("file") MultipartFile file) throws IOException {

        log.info("Render PDF: {} (size: {} bytes) to OCR optimized images",
            file.getOriginalFilename(), file.getSize());

        List<byte[]> imagePages = pdfRenderer.renderPagesToImages(file.getBytes(), PdfConstants.DEFAULT_DPI, new OcrOptimizedImageProcessor());
        return createZipResponse(file, imagePages);
    }

    private ResponseEntity<Resource> createZipResponse(MultipartFile file, List<byte[]> imagePages) {
        try {
            fileValidationService.validatePdfFile(file);

            byte[] zipBytes = zipArchiveService.createImageZipArchive(imagePages, file.getOriginalFilename(), "png");
            String outputFilename = zipArchiveService.generateZipFilename(file.getOriginalFilename());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/zip"));
            headers.setContentDispositionFormData("attachment", outputFilename);

            return ResponseEntity.ok()
                .headers(headers)
                .body(new ByteArrayResource(zipBytes));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF rendering: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());

        } catch (Exception e) {
            log.error("Error during PDF rendering: {}", e.getMessage(), e);
            return createErrorResponse("Failed to render PDF to images", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }
}
